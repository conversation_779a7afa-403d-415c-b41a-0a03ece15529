// Test script to verify export operations OTP throttling works correctly
// This script simulates the scenario where user fails OTP verification multiple times
// and then tries to export passphrase/private key again

const axios = require('axios');

const GRAPHQL_ENDPOINT = 'http://localhost:3000/graphql';
const TEST_EMAIL = '<EMAIL>';

async function makeGraphQLRequest(query, variables = {}, headers = {}) {
    try {
        const response = await axios.post(GRAPHQL_ENDPOINT, {
            query,
            variables
        }, {
            headers: {
                'Content-Type': 'application/json',
                ...headers
            }
        });
        
        return response.data;
    } catch (error) {
        console.error('GraphQL request failed:', error.response?.data || error.message);
        return { errors: [{ message: error.message }] };
    }
}

async function loginWithEmailOtp(email, otpId, otpCode, targetPublicKey) {
    const query = `
        mutation LoginWithEmailOtp($input: LoginWithEmailOtpInputDTO!) {
            loginWithEmailOtp(input: $input) {
                accessToken
                refreshToken
                userId
                subOrgId
            }
        }
    `;
    
    return await makeGraphQLRequest(query, {
        input: {
            email,
            otpId,
            otpCode,
            targetPublicKey
        }
    });
}

async function approveExportPassphrase(input, accessToken) {
    const query = `
        mutation ApproveExportPassphrase($input: ExportPassphraseInput!) {
            approveExportPassphrase(input: $input) {
                passphrase
                activityId
            }
        }
    `;
    
    return await makeGraphQLRequest(query, { input }, {
        'Authorization': `Bearer ${accessToken}`
    });
}

async function approveExportPrivateKey(input, accessToken) {
    const query = `
        mutation ApproveExportPrivateKey($input: ExportPrivateKeyInput!) {
            approveExportPrivateKey(input: $input) {
                privateKey
                activityId
            }
        }
    `;
    
    return await makeGraphQLRequest(query, { input }, {
        'Authorization': `Bearer ${accessToken}`
    });
}

async function testExportOtpThrottling() {
    console.log('🧪 Testing Export Operations OTP Throttling...\n');
    
    // Mock data for testing
    const mockExportPassphraseInput = {
        activityId: 'mock-activity-id-passphrase',
        publicKey: '0x' + '1'.repeat(64),
        message: 'mock-message',
        signature: '0x' + '2'.repeat(128),
        otpId: 'mock-otp-id',
        otpCode: 'wrong-code'
    };
    
    const mockExportPrivateKeyInput = {
        activityId: 'mock-activity-id-private-key',
        publicKey: '0x' + '3'.repeat(64),
        message: 'mock-message',
        signature: '0x' + '4'.repeat(128),
        otpId: 'mock-otp-id',
        otpCode: 'wrong-code'
    };
    
    const mockAccessToken = 'mock-access-token';
    
    console.log('1. Testing Export Passphrase with OTP Throttling...');
    
    // Simulate multiple failed OTP attempts in export passphrase
    console.log('   Simulating 10 failed OTP verification attempts in export passphrase...');
    for (let i = 1; i <= 10; i++) {
        console.log(`   Attempt ${i}/10: Trying wrong OTP code in export passphrase...`);
        const result = await approveExportPassphrase(mockExportPassphraseInput, mockAccessToken);
        
        if (result.errors) {
            console.log(`   ❌ Failed as expected: ${result.errors[0].message}`);
        } else {
            console.log('   ⚠️  Unexpected success');
        }
        
        // Small delay between attempts
        await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // Try export passphrase again (should be blocked due to OTP throttling)
    console.log('\n   Trying export passphrase again (should be blocked due to OTP throttling)...');
    const passphraseThrottleResult = await approveExportPassphrase(mockExportPassphraseInput, mockAccessToken);
    
    if (passphraseThrottleResult.errors) {
        const errorMessage = passphraseThrottleResult.errors[0].message;
        if (errorMessage.includes('Too many failed OTP verification attempts')) {
            console.log('   ✅ SUCCESS: Export passphrase correctly blocked due to OTP verification throttling');
            console.log('   Error message:', errorMessage);
        } else {
            console.log('   ❌ FAILED: Export passphrase blocked but with unexpected error:', errorMessage);
        }
    } else {
        console.log('   ❌ FAILED: Export passphrase should have been blocked but was allowed');
    }
    
    console.log('\n2. Testing Export Private Key with OTP Throttling...');
    
    // Simulate multiple failed OTP attempts in export private key
    console.log('   Simulating 10 failed OTP verification attempts in export private key...');
    for (let i = 1; i <= 10; i++) {
        console.log(`   Attempt ${i}/10: Trying wrong OTP code in export private key...`);
        const result = await approveExportPrivateKey(mockExportPrivateKeyInput, mockAccessToken);
        
        if (result.errors) {
            console.log(`   ❌ Failed as expected: ${result.errors[0].message}`);
        } else {
            console.log('   ⚠️  Unexpected success');
        }
        
        // Small delay between attempts
        await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // Try export private key again (should be blocked due to OTP throttling)
    console.log('\n   Trying export private key again (should be blocked due to OTP throttling)...');
    const privateKeyThrottleResult = await approveExportPrivateKey(mockExportPrivateKeyInput, mockAccessToken);
    
    if (privateKeyThrottleResult.errors) {
        const errorMessage = privateKeyThrottleResult.errors[0].message;
        if (errorMessage.includes('Too many failed OTP verification attempts')) {
            console.log('   ✅ SUCCESS: Export private key correctly blocked due to OTP verification throttling');
            console.log('   Error message:', errorMessage);
        } else {
            console.log('   ❌ FAILED: Export private key blocked but with unexpected error:', errorMessage);
        }
    } else {
        console.log('   ❌ FAILED: Export private key should have been blocked but was allowed');
    }
    
    console.log('\n3. Testing Cross-Operation Throttling...');
    console.log('   Testing if OTP throttling from export passphrase affects export private key...');
    
    // This should also be blocked since both operations check the same email throttle
    const crossThrottleResult = await approveExportPrivateKey({
        ...mockExportPrivateKeyInput,
        otpId: 'different-otp-id'
    }, mockAccessToken);
    
    if (crossThrottleResult.errors) {
        const errorMessage = crossThrottleResult.errors[0].message;
        if (errorMessage.includes('Too many failed OTP verification attempts')) {
            console.log('   ✅ SUCCESS: Cross-operation throttling works correctly');
            console.log('   Error message:', errorMessage);
        } else {
            console.log('   ❌ FAILED: Cross-operation blocked but with unexpected error:', errorMessage);
        }
    } else {
        console.log('   ❌ FAILED: Cross-operation should have been blocked but was allowed');
    }
    
    console.log('\n🏁 Test completed!');
    console.log('\n📋 Summary:');
    console.log('- Export operations now check OTP verification throttling by email');
    console.log('- Users who fail OTP verification 10+ times are blocked for 15 minutes');
    console.log('- Throttling applies across all export operations for the same email');
    console.log('- Successful exports clear the OTP verification throttle');
    console.log('- This prevents abuse while maintaining good user experience');
}

// Run the test
testExportOtpThrottling().catch(console.error);
