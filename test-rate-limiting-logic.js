// Simple test to verify the rate limiting logic
// This tests the new methods we added to RateLimitingService

const crypto = require('crypto');

// Mock Redis client
class MockRedis {
    constructor() {
        this.data = new Map();
    }
    
    pipeline() {
        const commands = [];
        return {
            zremrangebyscore: (key, min, max) => {
                commands.push(['zremrangebyscore', key, min, max]);
                return this;
            },
            zcard: (key) => {
                commands.push(['zcard', key]);
                return this;
            },
            zadd: (key, score, member) => {
                commands.push(['zadd', key, score, member]);
                return this;
            },
            expire: (key, seconds) => {
                commands.push(['expire', key, seconds]);
                return this;
            },
            exec: async () => {
                // Simulate Redis operations
                const results = [];
                let currentCount = 0;
                
                for (const [cmd, key, ...args] of commands) {
                    if (cmd === 'zremrangebyscore') {
                        // Remove expired entries (simulate)
                        results.push([null, 0]);
                    } else if (cmd === 'zcard') {
                        // Get current count
                        const count = this.data.get(key) || 0;
                        currentCount = count;
                        results.push([null, count]);
                    } else if (cmd === 'zadd') {
                        // Add new entry
                        const newCount = currentCount + 1;
                        this.data.set(key, newCount);
                        results.push([null, 1]);
                    } else if (cmd === 'expire') {
                        // Set expiration (simulate)
                        results.push([null, 1]);
                    }
                }
                
                return results;
            }
        };
    }
    
    del(key) {
        this.data.delete(key);
        return Promise.resolve(1);
    }
}

// Mock RateLimitingService
class MockRateLimitingService {
    constructor() {
        this.redis = new MockRedis();
        this.logger = {
            info: (...args) => console.log('INFO:', ...args),
            warn: (...args) => console.log('WARN:', ...args),
            error: (...args) => console.log('ERROR:', ...args)
        };
    }
    
    hashIdentifier(identifier, salt = 'default_salt') {
        return crypto.createHash('sha256').update(identifier + salt).digest('hex');
    }
    
    // Copy the new methods from RateLimitingService
    async recordFailedOtpVerificationByEmail(email, config) {
        const hashedEmail = this.hashIdentifier(email, 'otp_verification_email_salt');
        const redisKey = `${config.keyPrefix}_by_email:${hashedEmail}`;
        const now = Date.now();
        const windowStart = now - config.windowMs;

        try {
            const redis = this.redis;

            // Use Redis pipeline for atomic operations
            const pipeline = redis.pipeline();

            // Clean up expired entries
            pipeline.zremrangebyscore(redisKey, 0, windowStart);

            // Add current failed attempt
            pipeline.zadd(redisKey, now, `${now}-${Math.random()}`);

            // Set expiration for the key
            pipeline.expire(redisKey, Math.ceil(config.windowMs / 1000));

            await pipeline.exec();

            this.logger.info(
                {
                    hashedEmail,
                    keyPrefix: config.keyPrefix,
                },
                'Failed OTP verification attempt recorded by email',
            );
        } catch (error) {
            this.logger.error(
                {
                    error: error.message,
                    stack: error.stack,
                    hashedEmail,
                    config,
                },
                'Error recording failed OTP verification attempt by email',
            );
        }
    }

    async checkOtpVerificationThrottleByEmail(email, config) {
        const hashedEmail = this.hashIdentifier(email, 'otp_verification_email_salt');
        const redisKey = `${config.keyPrefix}_by_email:${hashedEmail}`;
        const now = Date.now();
        const windowStart = now - config.windowMs;

        try {
            const redis = this.redis;

            // Use Redis pipeline for atomic operations
            const pipeline = redis.pipeline();

            // Clean up expired entries
            pipeline.zremrangebyscore(redisKey, 0, windowStart);

            // Count current attempts
            pipeline.zcard(redisKey);

            const results = await pipeline.exec();

            if (!results || results.length < 2) {
                throw new Error('Redis pipeline failed');
            }

            const currentCount = results[1][1];
            const allowed = currentCount < config.maxRequests;
            const remainingAttempts = Math.max(0, config.maxRequests - currentCount);
            const resetTime = now + config.windowMs;

            if (!allowed) {
                this.logger.warn(
                    {
                        hashedEmail,
                        currentCount,
                        maxRequests: config.maxRequests,
                        windowMs: config.windowMs,
                        keyPrefix: config.keyPrefix,
                    },
                    'OTP verification throttled by email due to too many failed attempts',
                );
            }

            return {
                allowed,
                remainingRequests: remainingAttempts,
                resetTime,
                totalHits: currentCount,
            };
        } catch (error) {
            this.logger.error(
                {
                    error: error.message,
                    stack: error.stack,
                    hashedEmail,
                    config,
                },
                'Error checking OTP verification throttle by email',
            );

            // In case of error, allow the request to proceed
            return {
                allowed: true,
                remainingRequests: config.maxRequests,
                resetTime: now + config.windowMs,
                totalHits: 0,
            };
        }
    }
}

// Test the logic
async function testRateLimitingLogic() {
    console.log('🧪 Testing Rate Limiting Logic...\n');
    
    const service = new MockRateLimitingService();
    const testEmail = '<EMAIL>';
    const config = {
        windowMs: 15 * 60 * 1000, // 15 minutes
        maxRequests: 10, // 10 failed attempts
        keyPrefix: 'otp_verification_throttle'
    };
    
    // Test 1: Check initial state (should be allowed)
    console.log('1. Testing initial state...');
    let result = await service.checkOtpVerificationThrottleByEmail(testEmail, config);
    console.log('   Allowed:', result.allowed, '| Remaining:', result.remainingRequests, '| Total hits:', result.totalHits);
    
    if (result.allowed && result.totalHits === 0) {
        console.log('   ✅ Initial state correct');
    } else {
        console.log('   ❌ Initial state incorrect');
    }
    
    // Test 2: Record 10 failed attempts
    console.log('\n2. Recording 10 failed attempts...');
    for (let i = 1; i <= 10; i++) {
        await service.recordFailedOtpVerificationByEmail(testEmail, config);
        console.log(`   Recorded attempt ${i}/10`);
    }
    
    // Test 3: Check if throttled after 10 attempts
    console.log('\n3. Checking throttle status after 10 attempts...');
    result = await service.checkOtpVerificationThrottleByEmail(testEmail, config);
    console.log('   Allowed:', result.allowed, '| Remaining:', result.remainingRequests, '| Total hits:', result.totalHits);
    
    if (!result.allowed && result.totalHits >= 10) {
        console.log('   ✅ Correctly throttled after 10 attempts');
    } else {
        console.log('   ❌ Should be throttled but is not');
    }
    
    // Test 4: Test different email (should be allowed)
    console.log('\n4. Testing different email...');
    const differentEmail = '<EMAIL>';
    result = await service.checkOtpVerificationThrottleByEmail(differentEmail, config);
    console.log('   Allowed:', result.allowed, '| Remaining:', result.remainingRequests, '| Total hits:', result.totalHits);
    
    if (result.allowed && result.totalHits === 0) {
        console.log('   ✅ Different email correctly allowed');
    } else {
        console.log('   ❌ Different email should be allowed');
    }
    
    console.log('\n🏁 Test completed!');
}

// Run the test
testRateLimitingLogic().catch(console.error);
