// Rate limiting time window constants
export const RATE_LIMIT_WINDOW_MS = 15 * 60 * 1000; // 15 minutes

// Email OTP rate limiting
export const EMAIL_OTP_INIT_MAX_REQUESTS = 10; // 10 requests per window
export const EMAIL_OTP_LOGIN_MAX_REQUESTS = 10; // 10 requests per window

// Email/Fingerprint specific rate limiting for initOtpAuth
export const EMAIL_RATE_LIMIT_WINDOW_MS = 60 * 1000; // 1 minute
export const EMAIL_RATE_LIMIT_MAX_REQUESTS = 2; // 2 request per minute per email
export const FINGERPRINT_RATE_LIMIT_WINDOW_MS = 60 * 1000; // 1 minute
export const FINGERPRINT_RATE_LIMIT_MAX_REQUESTS = 2; // 2 request per minute per fingerprint

// OTP verification throttling to prevent brute force attacks
export const OTP_VERIFICATION_THROTTLE_WINDOW_MS = 15 * 60 * 1000; // 15 minutes
export const OTP_VERIFICATION_THROTTLE_MAX_ATTEMPTS = 10; // 10 failed attempts per OTP_ID before throttling
