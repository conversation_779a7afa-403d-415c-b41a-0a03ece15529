import { Injectable } from '@nestjs/common';
import { RedisService } from 'libs/redis/src';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import * as crypto from 'crypto';
import {
    EMAIL_RATE_LIMIT_WINDOW_MS,
    EMAIL_RATE_LIMIT_MAX_REQUESTS,
    RATE_LIMIT_WINDOW_MS,
    EMAIL_OTP_INIT_MAX_REQUESTS,
    EMAIL_OTP_LOGIN_MAX_REQUESTS,
} from './rate-limiting.constants';

export interface RateLimitConfig {
    windowMs: number; // Time window in milliseconds
    maxRequests: number; // Maximum requests per window
    keyPrefix: string; // Redis key prefix for this rate limit type
}

export interface RateLimitResult {
    allowed: boolean;
    remainingRequests: number;
    resetTime: number; // Unix timestamp when the window resets
    totalHits: number;
}

@Injectable()
export class RateLimitingService {
    constructor(
        private readonly redisService: RedisService,
        @InjectPinoLogger(RateLimitingService.name)
        private readonly logger: PinoLogger,
    ) {}

    /**
     * Hash IP address for privacy while maintaining uniqueness
     */
    private hashIpAddress(ipAddress: string): string {
        return crypto
            .createHash('sha256')
            .update(ipAddress + 'rate_limit_salt')
            .digest('hex');
    }

    /**
     * Check if a request is allowed based on rate limiting rules
     */
    async checkRateLimit(ipAddress: string, config: RateLimitConfig): Promise<RateLimitResult> {
        const hashedIp = this.hashIpAddress(ipAddress);
        const redisKey = `${config.keyPrefix}:${hashedIp}`;
        const now = Date.now();
        const windowStart = now - config.windowMs;

        try {
            const redis = this.redisService.getClient();

            // Use Redis pipeline for atomic operations
            const pipeline = redis.pipeline();

            // Remove expired entries
            pipeline.zremrangebyscore(redisKey, 0, windowStart);

            // Count current requests in window
            pipeline.zcard(redisKey);

            // Add current request
            pipeline.zadd(redisKey, now, `${now}-${Math.random()}`);

            // Set expiration for the key
            pipeline.expire(redisKey, Math.ceil(config.windowMs / 1000));

            const results = await pipeline.exec();

            if (!results || results.some(([err]) => err)) {
                this.logger.error('Redis pipeline execution failed for rate limiting');
                // Fail open - allow the request if Redis is having issues
                return {
                    allowed: true,
                    remainingRequests: config.maxRequests - 1,
                    resetTime: now + config.windowMs,
                    totalHits: 1,
                };
            }

            // Get the count after cleanup but before adding current request
            const currentCount = results[1][1] as number;
            const totalHits = currentCount + 1; // Including the current request we just added

            const allowed = totalHits <= config.maxRequests;
            const remainingRequests = Math.max(0, config.maxRequests - totalHits);
            const resetTime = now + config.windowMs;

            if (!allowed) {
                // Remove the request we just added since it's not allowed
                await redis.zrem(redisKey, `${now}-${Math.random()}`);

                this.logger.warn(
                    {
                        hashedIp,
                        totalHits,
                        maxRequests: config.maxRequests,
                        windowMs: config.windowMs,
                        keyPrefix: config.keyPrefix,
                    },
                    'Rate limit exceeded',
                );
            }

            return {
                allowed,
                remainingRequests,
                resetTime,
                totalHits: allowed ? totalHits : currentCount,
            };
        } catch (error) {
            this.logger.error(
                {
                    error: error.message,
                    stack: error.stack,
                    hashedIp,
                    config,
                },
                'Error checking rate limit',
            );

            // Fail open - allow the request if there's an error
            return {
                allowed: true,
                remainingRequests: config.maxRequests - 1,
                resetTime: now + config.windowMs,
                totalHits: 1,
            };
        }
    }

    /**
     * Get current rate limit status without incrementing
     */
    async getRateLimitStatus(ipAddress: string, config: RateLimitConfig): Promise<RateLimitResult> {
        const hashedIp = this.hashIpAddress(ipAddress);
        const redisKey = `${config.keyPrefix}:${hashedIp}`;
        const now = Date.now();
        const windowStart = now - config.windowMs;

        try {
            const redis = this.redisService.getClient();

            // Clean up expired entries and count current requests
            await redis.zremrangebyscore(redisKey, 0, windowStart);
            const currentCount = await redis.zcard(redisKey);

            const remainingRequests = Math.max(0, config.maxRequests - currentCount);
            const resetTime = now + config.windowMs;

            return {
                allowed: currentCount < config.maxRequests,
                remainingRequests,
                resetTime,
                totalHits: currentCount,
            };
        } catch (error) {
            this.logger.error(
                {
                    error: error.message,
                    stack: error.stack,
                    hashedIp,
                    config,
                },
                'Error getting rate limit status',
            );

            return {
                allowed: true,
                remainingRequests: config.maxRequests,
                resetTime: now + config.windowMs,
                totalHits: 0,
            };
        }
    }

    /**
     * Reset rate limit for a specific IP and config
     */
    async resetRateLimit(ipAddress: string, config: RateLimitConfig): Promise<void> {
        const hashedIp = this.hashIpAddress(ipAddress);
        const redisKey = `${config.keyPrefix}:${hashedIp}`;

        try {
            await this.redisService.getClient().del(redisKey);
            this.logger.info({ hashedIp, keyPrefix: config.keyPrefix }, 'Rate limit reset');
        } catch (error) {
            this.logger.error(
                {
                    error: error.message,
                    stack: error.stack,
                    hashedIp,
                    config,
                },
                'Error resetting rate limit',
            );
        }
    }

    /**
     * Hash any identifier for privacy while maintaining uniqueness
     */
    private hashIdentifier(identifier: string, salt: string = 'rate_limit_salt'): string {
        return crypto
            .createHash('sha256')
            .update(identifier + salt)
            .digest('hex');
    }

    /**
     * Check and record OTP verification attempt for throttling
     * This method tracks failed OTP verification attempts per OTP_ID to prevent brute force attacks
     */
    async checkOtpVerificationThrottle(otpId: string, config: RateLimitConfig): Promise<RateLimitResult> {
        const hashedOtpId = this.hashIdentifier(otpId, 'otp_verification_salt');
        const redisKey = `${config.keyPrefix}:${hashedOtpId}`;
        const now = Date.now();
        const windowStart = now - config.windowMs;

        try {
            const redis = this.redisService.getClient();

            // Use Redis pipeline for atomic operations
            const pipeline = redis.pipeline();

            // Clean up expired entries
            pipeline.zremrangebyscore(redisKey, 0, windowStart);

            // Count current failed attempts
            pipeline.zcard(redisKey);

            const results = await pipeline.exec();

            if (!results || results.some(([err]) => err)) {
                throw new Error('Redis pipeline execution failed');
            }

            const currentCount = results[1][1] as number;
            const allowed = currentCount < config.maxRequests;
            const remainingAttempts = Math.max(0, config.maxRequests - currentCount);
            const resetTime = now + config.windowMs;

            if (!allowed) {
                this.logger.warn(
                    {
                        hashedOtpId,
                        currentCount,
                        maxRequests: config.maxRequests,
                        windowMs: config.windowMs,
                        keyPrefix: config.keyPrefix,
                    },
                    'OTP verification throttled due to too many failed attempts',
                );
            }

            return {
                allowed,
                remainingRequests: remainingAttempts,
                resetTime,
                totalHits: currentCount,
            };
        } catch (error) {
            this.logger.error(
                {
                    error: error.message,
                    stack: error.stack,
                    hashedOtpId,
                    config,
                },
                'Error checking OTP verification throttle',
            );

            // In case of error, allow the request to proceed
            return {
                allowed: true,
                remainingRequests: config.maxRequests,
                resetTime: now + config.windowMs,
                totalHits: 0,
            };
        }
    }

    /**
     * Record a failed OTP verification attempt for throttling
     */
    async recordFailedOtpVerification(otpId: string, config: RateLimitConfig): Promise<void> {
        const hashedOtpId = this.hashIdentifier(otpId, 'otp_verification_salt');
        const redisKey = `${config.keyPrefix}:${hashedOtpId}`;
        const now = Date.now();
        const windowStart = now - config.windowMs;

        try {
            const redis = this.redisService.getClient();

            // Use Redis pipeline for atomic operations
            const pipeline = redis.pipeline();

            // Clean up expired entries
            pipeline.zremrangebyscore(redisKey, 0, windowStart);

            // Add current failed attempt
            pipeline.zadd(redisKey, now, `${now}-${Math.random()}`);

            // Set expiration for the key
            pipeline.expire(redisKey, Math.ceil(config.windowMs / 1000));

            await pipeline.exec();

            this.logger.info(
                {
                    hashedOtpId,
                    keyPrefix: config.keyPrefix,
                },
                'Failed OTP verification attempt recorded',
            );
        } catch (error) {
            this.logger.error(
                {
                    error: error.message,
                    stack: error.stack,
                    hashedOtpId,
                    config,
                },
                'Error recording failed OTP verification attempt',
            );
        }
    }

    /**
     * Record a failed OTP verification attempt for throttling by email
     * This allows us to track failed attempts by email address for blocking initOtp
     */
    async recordFailedOtpVerificationByEmail(email: string, config: RateLimitConfig): Promise<void> {
        const hashedEmail = this.hashIdentifier(email, 'otp_verification_email_salt');
        const redisKey = `${config.keyPrefix}_by_email:${hashedEmail}`;
        const now = Date.now();
        const windowStart = now - config.windowMs;

        try {
            const redis = this.redisService.getClient();

            // Use Redis pipeline for atomic operations
            const pipeline = redis.pipeline();

            // Clean up expired entries
            pipeline.zremrangebyscore(redisKey, 0, windowStart);

            // Add current failed attempt
            pipeline.zadd(redisKey, now, `${now}-${Math.random()}`);

            // Set expiration for the key
            pipeline.expire(redisKey, Math.ceil(config.windowMs / 1000));

            await pipeline.exec();

            this.logger.info(
                {
                    hashedEmail,
                    keyPrefix: config.keyPrefix,
                },
                'Failed OTP verification attempt recorded by email',
            );
        } catch (error) {
            this.logger.error(
                {
                    error: error.message,
                    stack: error.stack,
                    hashedEmail,
                    config,
                },
                'Error recording failed OTP verification attempt by email',
            );
        }
    }

    /**
     * Check OTP verification throttle status by email
     * This prevents initOtp when user is already throttled from too many failed attempts
     */
    async checkOtpVerificationThrottleByEmail(email: string, config: RateLimitConfig): Promise<RateLimitResult> {
        const hashedEmail = this.hashIdentifier(email, 'otp_verification_email_salt');
        const redisKey = `${config.keyPrefix}_by_email:${hashedEmail}`;
        const now = Date.now();
        const windowStart = now - config.windowMs;

        try {
            const redis = this.redisService.getClient();

            // Use Redis pipeline for atomic operations
            const pipeline = redis.pipeline();

            // Clean up expired entries
            pipeline.zremrangebyscore(redisKey, 0, windowStart);

            // Count current attempts
            pipeline.zcard(redisKey);

            const results = await pipeline.exec();

            if (!results || results.length < 2) {
                throw new Error('Redis pipeline failed');
            }

            const currentCount = results[1][1] as number;
            const allowed = currentCount < config.maxRequests;
            const remainingAttempts = Math.max(0, config.maxRequests - currentCount);
            const resetTime = now + config.windowMs;

            if (!allowed) {
                this.logger.warn(
                    {
                        hashedEmail,
                        currentCount,
                        maxRequests: config.maxRequests,
                        windowMs: config.windowMs,
                        keyPrefix: config.keyPrefix,
                    },
                    'OTP verification throttled by email due to too many failed attempts',
                );
            }

            return {
                allowed,
                remainingRequests: remainingAttempts,
                resetTime,
                totalHits: currentCount,
            };
        } catch (error) {
            this.logger.error(
                {
                    error: error.message,
                    stack: error.stack,
                    hashedEmail,
                    config,
                },
                'Error checking OTP verification throttle by email',
            );

            // In case of error, allow the request to proceed
            return {
                allowed: true,
                remainingRequests: config.maxRequests,
                resetTime: now + config.windowMs,
                totalHits: 0,
            };
        }
    }

    /**
     * Check rate limit for a specific identifier (email, fingerprint, etc.)
     */
    async checkRateLimitByIdentifier(identifier: string, config: RateLimitConfig): Promise<RateLimitResult> {
        const hashedIdentifier = this.hashIdentifier(identifier);
        const redisKey = `${config.keyPrefix}:${hashedIdentifier}`;
        const now = Date.now();
        const windowStart = now - config.windowMs;

        try {
            const redis = this.redisService.getClient();

            // Use Redis pipeline for atomic operations
            const pipeline = redis.pipeline();

            // Remove expired entries
            pipeline.zremrangebyscore(redisKey, 0, windowStart);

            // Count current requests in window
            pipeline.zcard(redisKey);

            // Add current request
            pipeline.zadd(redisKey, now, `${now}-${Math.random()}`);

            // Set expiration for the key
            pipeline.expire(redisKey, Math.ceil(config.windowMs / 1000));

            const results = await pipeline.exec();

            if (!results || results.some(([err]) => err)) {
                this.logger.error('Redis pipeline execution failed for identifier rate limiting');
                // Fail open - allow the request if Redis is having issues
                return {
                    allowed: true,
                    remainingRequests: config.maxRequests - 1,
                    resetTime: now + config.windowMs,
                    totalHits: 1,
                };
            }

            // Get the count after cleanup but before adding current request
            const currentCount = results[1][1] as number;
            const totalHits = currentCount + 1; // Including the current request we just added

            const allowed = totalHits <= config.maxRequests;
            const remainingRequests = Math.max(0, config.maxRequests - totalHits);
            const resetTime = now + config.windowMs;

            if (!allowed) {
                // Remove the request we just added since it's not allowed
                await redis.zrem(redisKey, `${now}-${Math.random()}`);

                this.logger.warn(
                    {
                        hashedIdentifier,
                        totalHits,
                        maxRequests: config.maxRequests,
                        windowMs: config.windowMs,
                        keyPrefix: config.keyPrefix,
                    },
                    'Rate limit exceeded for identifier',
                );
            }

            return {
                allowed,
                remainingRequests,
                resetTime,
                totalHits: allowed ? totalHits : currentCount,
            };
        } catch (error) {
            this.logger.error(
                {
                    error: error.message,
                    stack: error.stack,
                    hashedIdentifier: this.hashIdentifier(identifier),
                    config,
                },
                'Error checking rate limit by identifier',
            );

            // Fail open - allow the request if there's an error
            return {
                allowed: true,
                remainingRequests: config.maxRequests - 1,
                resetTime: now + config.windowMs,
                totalHits: 1,
            };
        }
    }

    /**
     * Get current rate limit status for a specific identifier without incrementing
     */
    async getRateLimitStatusByIdentifier(identifier: string, config: RateLimitConfig): Promise<RateLimitResult> {
        const hashedIdentifier = this.hashIdentifier(identifier);
        const redisKey = `${config.keyPrefix}:${hashedIdentifier}`;
        const now = Date.now();
        const windowStart = now - config.windowMs;

        try {
            const redis = this.redisService.getClient();

            // Clean up expired entries and count current requests
            await redis.zremrangebyscore(redisKey, 0, windowStart);
            const currentCount = await redis.zcard(redisKey);

            const remainingRequests = Math.max(0, config.maxRequests - currentCount);
            const resetTime = now + config.windowMs;

            return {
                allowed: currentCount < config.maxRequests,
                remainingRequests,
                resetTime,
                totalHits: currentCount,
            };
        } catch (error) {
            this.logger.error(
                {
                    error: error.message,
                    stack: error.stack,
                    hashedIdentifier,
                    config,
                },
                'Error getting rate limit status by identifier',
            );

            return {
                allowed: true,
                remainingRequests: config.maxRequests,
                resetTime: now + config.windowMs,
                totalHits: 0,
            };
        }
    }

    /**
     * Reset rate limit for a specific identifier (email, fingerprint, etc.)
     */
    async resetRateLimitByIdentifier(identifier: string, config: RateLimitConfig): Promise<void> {
        const hashedIdentifier = this.hashIdentifier(identifier);
        const redisKey = `${config.keyPrefix}:${hashedIdentifier}`;

        try {
            await this.redisService.getClient().del(redisKey);
            this.logger.info({ hashedIdentifier, keyPrefix: config.keyPrefix }, 'Rate limit reset for identifier');
        } catch (error) {
            this.logger.error(
                {
                    error: error.message,
                    stack: error.stack,
                    hashedIdentifier,
                    config,
                },
                'Error resetting rate limit for identifier',
            );
        }
    }

    /**
     * Clear OTP verification throttle by email
     * This allows user to retry OTP verification after successful operations
     */
    async clearOtpVerificationThrottleByEmail(email: string): Promise<void> {
        try {
            const hashedEmail = this.hashIdentifier(email, 'otp_verification_email_salt');
            const otpThrottleKey = `otp_verification_throttle_by_email:${hashedEmail}`;
            await this.redisService.getClient().del(otpThrottleKey);

            this.logger.info(
                {
                    email,
                },
                'OTP verification throttle cleared by email',
            );
        } catch (error) {
            this.logger.error(
                {
                    error: error.message,
                    stack: error.stack,
                    email,
                },
                'Error clearing OTP verification throttle by email',
            );
        }
    }

    /**
     * Clear all rate limits for successful email OTP authentication
     * This clears both IP-based and email-based rate limits to allow immediate re-login with different email
     */
    async clearEmailOtpRateLimits(email: string, ipAddress: string): Promise<void> {
        try {
            // Clear email-based rate limit for initOtpAuth
            await this.resetRateLimitByIdentifier(email, {
                windowMs: EMAIL_RATE_LIMIT_WINDOW_MS,
                maxRequests: EMAIL_RATE_LIMIT_MAX_REQUESTS,
                keyPrefix: 'email_otp_init_by_email',
            });

            // Clear OTP verification throttle by email to allow immediate re-login
            const hashedEmail = this.hashIdentifier(email, 'otp_verification_email_salt');
            const otpThrottleKey = `otp_verification_throttle_by_email:${hashedEmail}`;
            await this.redisService.getClient().del(otpThrottleKey);

            // Clear IP-based rate limits for both init and login
            await this.resetRateLimit(ipAddress, {
                windowMs: RATE_LIMIT_WINDOW_MS,
                maxRequests: EMAIL_OTP_INIT_MAX_REQUESTS,
                keyPrefix: 'email_otp_init',
            });

            await this.resetRateLimit(ipAddress, {
                windowMs: RATE_LIMIT_WINDOW_MS,
                maxRequests: EMAIL_OTP_LOGIN_MAX_REQUESTS,
                keyPrefix: 'email_otp_login',
            });

            this.logger.info(
                {
                    email,
                    hashedIp: this.hashIpAddress(ipAddress),
                },
                'Email OTP rate limits and verification throttle cleared after successful authentication',
            );
        } catch (error) {
            this.logger.error(
                {
                    error: error.message,
                    stack: error.stack,
                    email,
                    hashedIp: this.hashIpAddress(ipAddress),
                },
                'Error clearing email OTP rate limits',
            );
        }
    }
}
