// Test script to verify <PERSON><PERSON> throttling by email works correctly
// This script simulates the scenario where user fails OTP verification multiple times
// and then tries to initEmailOtp again

const axios = require('axios');

const GRAPHQL_ENDPOINT = 'http://localhost:3000/graphql';
const TEST_EMAIL = '<EMAIL>';

async function makeGraphQLRequest(query, variables = {}) {
    try {
        const response = await axios.post(GRAPHQL_ENDPOINT, {
            query,
            variables
        }, {
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        return response.data;
    } catch (error) {
        console.error('GraphQL request failed:', error.response?.data || error.message);
        return { errors: [{ message: error.message }] };
    }
}

async function initEmailOtp(email, fingerprint = 'test-fingerprint') {
    const query = `
        mutation InitEmailOtp($input: InitEmailOtpInputDTO!) {
            initEmailOtp(input: $input) {
                otpId
                userId
                subOrgId
                ttl
            }
        }
    `;
    
    return await makeGraphQLRequest(query, {
        input: {
            email,
            fingerprint
        }
    });
}

async function loginWithEmailOtp(email, otpId, otpCode, targetPublicKey) {
    const query = `
        mutation LoginWithEmailOtp($input: LoginWithEmailOtpInputDTO!) {
            loginWithEmailOtp(input: $input) {
                accessToken
                refreshToken
                userId
                subOrgId
            }
        }
    `;
    
    return await makeGraphQLRequest(query, {
        input: {
            email,
            otpId,
            otpCode,
            targetPublicKey
        }
    });
}

async function testOtpThrottling() {
    console.log('🧪 Testing OTP throttling by email...\n');
    
    // Step 1: Initialize OTP
    console.log('1. Initializing OTP for email:', TEST_EMAIL);
    const initResult = await initEmailOtp(TEST_EMAIL);
    
    if (initResult.errors) {
        console.error('❌ Failed to initialize OTP:', initResult.errors[0].message);
        return;
    }
    
    const otpId = initResult.data.initEmailOtp.otpId;
    console.log('✅ OTP initialized successfully. OTP ID:', otpId);
    
    // Step 2: Simulate multiple failed OTP verification attempts
    console.log('\n2. Simulating 10 failed OTP verification attempts...');
    
    const targetPublicKey = '0x' + '0'.repeat(64); // Dummy public key
    
    for (let i = 1; i <= 10; i++) {
        console.log(`   Attempt ${i}/10: Trying wrong OTP code...`);
        const loginResult = await loginWithEmailOtp(TEST_EMAIL, otpId, 'wrong-code', targetPublicKey);
        
        if (loginResult.errors) {
            console.log(`   ❌ Failed as expected: ${loginResult.errors[0].message}`);
        } else {
            console.log('   ⚠️  Unexpected success');
        }
        
        // Small delay between attempts
        await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // Step 3: Try to initialize OTP again (should be blocked)
    console.log('\n3. Trying to initialize OTP again (should be blocked due to throttling)...');
    const secondInitResult = await initEmailOtp(TEST_EMAIL);
    
    if (secondInitResult.errors) {
        const errorMessage = secondInitResult.errors[0].message;
        if (errorMessage.includes('throttled') || errorMessage.includes('wait')) {
            console.log('✅ SUCCESS: initEmailOtp is correctly blocked due to OTP verification throttling');
            console.log('   Error message:', errorMessage);
        } else {
            console.log('❌ FAILED: initEmailOtp blocked but with unexpected error:', errorMessage);
        }
    } else {
        console.log('❌ FAILED: initEmailOtp should have been blocked but was allowed');
        console.log('   New OTP ID:', secondInitResult.data.initEmailOtp.otpId);
    }
    
    // Step 4: Test with different email (should work)
    console.log('\n4. Testing with different email (should work)...');
    const differentEmail = '<EMAIL>';
    const differentEmailResult = await initEmailOtp(differentEmail);
    
    if (differentEmailResult.errors) {
        console.log('❌ FAILED: Different email should work but got error:', differentEmailResult.errors[0].message);
    } else {
        console.log('✅ SUCCESS: Different email works correctly');
        console.log('   OTP ID:', differentEmailResult.data.initEmailOtp.otpId);
    }
    
    console.log('\n🏁 Test completed!');
}

// Run the test
testOtpThrottling().catch(console.error);
