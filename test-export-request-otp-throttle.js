// Test script to verify export operations request OTP throttling works correctly
// This script simulates the scenario where user fails OTP verification multiple times
// and then tries to request new OTP for export operations

const axios = require('axios');

const GRAPHQL_ENDPOINT = 'http://localhost:3000/graphql';
const TEST_EMAIL = '<EMAIL>';

async function makeGraphQLRequest(query, variables = {}, headers = {}) {
    try {
        const response = await axios.post(GRAPHQL_ENDPOINT, {
            query,
            variables
        }, {
            headers: {
                'Content-Type': 'application/json',
                ...headers
            }
        });
        
        return response.data;
    } catch (error) {
        console.error('GraphQL request failed:', error.response?.data || error.message);
        return { errors: [{ message: error.message }] };
    }
}

async function requestReverifyOtp(input, accessToken) {
    const query = `
        mutation RequestReverifyOtp($input: RequestReverifyOtpInputDto!) {
            requestReverifyOtp(input: $input) {
                otpId
                userId
                subOrgId
                ttl
            }
        }
    `;
    
    return await makeGraphQLRequest(query, { input }, {
        'Authorization': `Bearer ${accessToken}`
    });
}

async function approveExportPassphrase(input, accessToken) {
    const query = `
        mutation ApproveExportPassphrase($input: ExportPassphraseInput!) {
            approveExportPassphrase(input: $input) {
                passphrase
                activityId
            }
        }
    `;
    
    return await makeGraphQLRequest(query, { input }, {
        'Authorization': `Bearer ${accessToken}`
    });
}

async function testExportRequestOtpThrottling() {
    console.log('🧪 Testing Export Request OTP Throttling...\n');
    
    const mockAccessToken = 'mock-access-token';
    const mockRequestReverifyInput = {
        fingerprint: 'test-fingerprint'
    };
    
    console.log('1. Testing Request Reverify OTP Throttling...');
    
    // Step 1: Request OTP successfully
    console.log('   Step 1: Requesting OTP for export...');
    const firstOtpResult = await requestReverifyOtp(mockRequestReverifyInput, mockAccessToken);
    
    if (firstOtpResult.errors) {
        console.log('   ❌ Failed to request first OTP:', firstOtpResult.errors[0].message);
        return;
    }
    
    const otpId = firstOtpResult.data?.requestReverifyOtp?.otpId || 'mock-otp-id';
    console.log('   ✅ First OTP request successful. OTP ID:', otpId);
    
    // Step 2: Simulate multiple failed OTP verification attempts in export
    console.log('\n   Step 2: Simulating 10 failed OTP verification attempts in export...');
    const mockExportInput = {
        activityId: 'mock-activity-id',
        publicKey: '0x' + '1'.repeat(64),
        message: 'mock-message',
        signature: '0x' + '2'.repeat(128),
        otpId: otpId,
        otpCode: 'wrong-code'
    };
    
    for (let i = 1; i <= 10; i++) {
        console.log(`   Attempt ${i}/10: Trying wrong OTP code in export...`);
        const result = await approveExportPassphrase(mockExportInput, mockAccessToken);
        
        if (result.errors) {
            console.log(`   ❌ Failed as expected: ${result.errors[0].message}`);
        } else {
            console.log('   ⚠️  Unexpected success');
        }
        
        // Small delay between attempts
        await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // Step 3: Try to request new OTP (should be blocked due to OTP verification throttling)
    console.log('\n   Step 3: Trying to request new OTP (should be blocked due to OTP verification throttling)...');
    const secondOtpResult = await requestReverifyOtp(mockRequestReverifyInput, mockAccessToken);
    
    if (secondOtpResult.errors) {
        const errorMessage = secondOtpResult.errors[0].message;
        if (errorMessage.includes('Too many failed OTP verification attempts')) {
            console.log('   ✅ SUCCESS: Request reverify OTP correctly blocked due to OTP verification throttling');
            console.log('   Error message:', errorMessage);
        } else {
            console.log('   ❌ FAILED: Request reverify OTP blocked but with unexpected error:', errorMessage);
        }
    } else {
        console.log('   ❌ FAILED: Request reverify OTP should have been blocked but was allowed');
        console.log('   New OTP ID:', secondOtpResult.data.requestReverifyOtp.otpId);
    }
    
    console.log('\n2. Testing Rate Limiting Consistency...');
    
    // Test email rate limiting (2 requests per minute)
    console.log('   Testing email rate limiting (2 requests per minute)...');
    
    // This should also be blocked due to email rate limiting
    const emailRateLimitResult = await requestReverifyOtp({
        fingerprint: 'different-fingerprint'
    }, mockAccessToken);
    
    if (emailRateLimitResult.errors) {
        const errorMessage = emailRateLimitResult.errors[0].message;
        if (errorMessage.includes('Too many requests for this email') || 
            errorMessage.includes('Too many failed OTP verification attempts')) {
            console.log('   ✅ SUCCESS: Email rate limiting or OTP throttling working correctly');
            console.log('   Error message:', errorMessage);
        } else {
            console.log('   ❌ FAILED: Blocked but with unexpected error:', errorMessage);
        }
    } else {
        console.log('   ❌ FAILED: Should have been blocked but was allowed');
    }
    
    console.log('\n3. Testing Fingerprint Rate Limiting...');
    
    // Test fingerprint rate limiting (2 requests per minute)
    console.log('   Testing fingerprint rate limiting (2 requests per minute)...');
    
    const fingerprintRateLimitResult = await requestReverifyOtp({
        fingerprint: 'test-fingerprint' // Same fingerprint as before
    }, mockAccessToken);
    
    if (fingerprintRateLimitResult.errors) {
        const errorMessage = fingerprintRateLimitResult.errors[0].message;
        if (errorMessage.includes('Too many requests from this device') || 
            errorMessage.includes('Too many failed OTP verification attempts')) {
            console.log('   ✅ SUCCESS: Fingerprint rate limiting or OTP throttling working correctly');
            console.log('   Error message:', errorMessage);
        } else {
            console.log('   ❌ FAILED: Blocked but with unexpected error:', errorMessage);
        }
    } else {
        console.log('   ❌ FAILED: Should have been blocked but was allowed');
    }
    
    console.log('\n🏁 Test completed!');
    console.log('\n📋 Summary:');
    console.log('- requestReverifyOtp now checks OTP verification throttling by email');
    console.log('- Users who fail OTP verification 10+ times cannot request new OTP for 15 minutes');
    console.log('- Email rate limiting: 2 requests per minute per email');
    console.log('- Fingerprint rate limiting: 2 requests per minute per fingerprint');
    console.log('- IP rate limiting: 10 requests per 15 minutes per IP');
    console.log('- All rate limits are cleared on successful OTP request');
    console.log('- This prevents meaningless OTP requests when user is already throttled');
}

// Run the test
testExportRequestOtpThrottling().catch(console.error);
